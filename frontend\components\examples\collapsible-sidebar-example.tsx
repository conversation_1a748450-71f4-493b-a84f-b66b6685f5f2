"use client"

import React from "react"
import {
  HomeIcon,
  SettingsIcon,
  UserIcon,
  FileIcon,
  FolderIcon,
  SearchIcon,
  BellIcon,
  MailIcon,
  CalendarIcon,
  BarChartIcon,
  DatabaseIcon,
  ShieldIcon,
  HelpCircleIcon,
  LogOutIcon,
} from "lucide-react"

import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarTrigger,
  SidebarInset,
  CollapsibleSidebarMenu,
  MenuItem,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// 示例菜单数据
const menuItems: MenuItem[] = [
  {
    id: "dashboard",
    label: "仪表板",
    icon: HomeIcon,
    href: "/dashboard",
    isActive: true,
  },
  {
    id: "projects",
    label: "项目管理",
    icon: FolderIcon,
    children: [
      {
        id: "all-projects",
        label: "所有项目",
        icon: FileIcon,
        href: "/projects",
      },
      {
        id: "my-projects",
        label: "我的项目",
        icon: UserIcon,
        href: "/projects/my",
        badge: 5,
      },
      {
        id: "archived",
        label: "已归档",
        icon: DatabaseIcon,
        href: "/projects/archived",
      },
    ],
  },
  {
    id: "analytics",
    label: "数据分析",
    icon: BarChartIcon,
    children: [
      {
        id: "reports",
        label: "报告",
        icon: FileIcon,
        href: "/analytics/reports",
      },
      {
        id: "metrics",
        label: "指标",
        icon: BarChartIcon,
        href: "/analytics/metrics",
        badge: "新",
      },
    ],
  },
  {
    id: "search",
    label: "搜索",
    icon: SearchIcon,
    href: "/search",
  },
  {
    id: "notifications",
    label: "通知",
    icon: BellIcon,
    href: "/notifications",
    badge: 12,
  },
  {
    id: "messages",
    label: "消息",
    icon: MailIcon,
    href: "/messages",
    badge: 3,
  },
  {
    id: "calendar",
    label: "日历",
    icon: CalendarIcon,
    href: "/calendar",
  },
  {
    id: "security",
    label: "安全设置",
    icon: ShieldIcon,
    children: [
      {
        id: "permissions",
        label: "权限管理",
        icon: UserIcon,
        href: "/security/permissions",
      },
      {
        id: "audit-logs",
        label: "审计日志",
        icon: FileIcon,
        href: "/security/audit",
      },
    ],
  },
  {
    id: "settings",
    label: "设置",
    icon: SettingsIcon,
    href: "/settings",
  },
  {
    id: "help",
    label: "帮助中心",
    icon: HelpCircleIcon,
    href: "/help",
  },
]

export function CollapsibleSidebarExample() {
  const handleItemClick = (item: MenuItem) => {
    console.log("点击菜单项:", item)
    // 这里可以处理路由跳转或其他逻辑
    if (item.href) {
      // 例如: router.push(item.href)
    }
  }

  return (
    <SidebarProvider defaultOpen={false}>
      <div className="flex h-screen w-full">
        <Sidebar collapsible="icon" className="border-r">
          <SidebarHeader className="border-b border-sidebar-border">
            <div className="flex items-center gap-2 px-2 py-2">
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <HomeIcon className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">赫尔墨系统</span>
                <span className="truncate text-xs">管理平台</span>
              </div>
            </div>
          </SidebarHeader>

          <SidebarContent>
            <CollapsibleSidebarMenu 
              items={menuItems} 
              onItemClick={handleItemClick}
            />
          </SidebarContent>

          <SidebarFooter className="border-t border-sidebar-border">
            <div className="flex items-center gap-2 px-2 py-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/avatars/user.png" alt="用户头像" />
                <AvatarFallback>用户</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">张三</span>
                <span className="truncate text-xs">管理员</span>
              </div>
              <Button variant="ghost" size="icon" className="ml-auto size-8">
                <LogOutIcon className="size-4" />
              </Button>
            </div>
          </SidebarFooter>
        </Sidebar>

        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
            <SidebarTrigger className="-ml-1" />
            <div className="ml-auto flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                可折叠侧边栏示例
              </span>
            </div>
          </header>
          
          <div className="flex flex-1 flex-col gap-4 p-4">
            <div className="grid auto-rows-min gap-4 md:grid-cols-3">
              <div className="aspect-video rounded-xl bg-muted/50" />
              <div className="aspect-video rounded-xl bg-muted/50" />
              <div className="aspect-video rounded-xl bg-muted/50" />
            </div>
            <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
              <div className="p-6">
                <h2 className="text-2xl font-bold mb-4">主要功能</h2>
                <ul className="space-y-2 text-sm">
                  <li>✅ 默认折叠状态，只显示图标</li>
                  <li>✅ 鼠标悬停显示 Tooltip 提示</li>
                  <li>✅ 支持多级子菜单展开/收起</li>
                  <li>✅ 当前激活菜单项特殊样式</li>
                  <li>✅ 使用 SVG 图标，支持自定义</li>
                  <li>✅ 响应式设计，移动端友好</li>
                  <li>✅ 键盘快捷键支持 (Ctrl/Cmd + B)</li>
                  <li>✅ 菜单项徽章显示</li>
                </ul>
              </div>
            </div>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}
