"use client"

import React from "react"
import {
  HomeIcon,
  SettingsIcon,
  UserIcon,
  FileIcon,
  FolderIcon,
  BarChartIcon,
} from "lucide-react"

import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  CollapsibleSidebarMenu,
  MenuItem,
} from "@/components/ui/sidebar"

// 简单的菜单配置示例
const simpleMenuItems: MenuItem[] = [
  {
    id: "home",
    label: "首页",
    icon: HomeIcon,
    isActive: true,
  },
  {
    id: "projects",
    label: "项目",
    icon: FolderIcon,
    children: [
      {
        id: "all-projects",
        label: "所有项目",
        icon: FileIcon,
      },
      {
        id: "my-projects",
        label: "我的项目",
        icon: UserIcon,
        badge: 3,
      },
    ],
  },
  {
    id: "analytics",
    label: "分析",
    icon: BarChartIcon,
    badge: "新",
  },
  {
    id: "settings",
    label: "设置",
    icon: SettingsIcon,
  },
]

export function SimpleSidebarUsage() {
  const handleMenuClick = (item: MenuItem) => {
    console.log("菜单点击:", item.label)
  }

  return (
    <SidebarProvider defaultOpen={false}>
      <div className="flex h-screen">
        <Sidebar collapsible="icon">
          <SidebarContent>
            <CollapsibleSidebarMenu 
              items={simpleMenuItems}
              onItemClick={handleMenuClick}
            />
          </SidebarContent>
        </Sidebar>
        
        <main className="flex-1 p-6">
          <h1 className="text-2xl font-bold mb-4">简单侧边栏使用示例</h1>
          <p className="text-muted-foreground">
            这是一个最简单的可折叠侧边栏使用示例。
          </p>
        </main>
      </div>
    </SidebarProvider>
  )
}
