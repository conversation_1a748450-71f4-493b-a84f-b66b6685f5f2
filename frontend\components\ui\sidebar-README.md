# 可折叠侧边栏组件

这是一个完全重构的可折叠垂直侧边栏菜单组件，支持多级子菜单、图标显示、Tooltip 提示等功能。

## 主要特性

- ✅ **默认折叠状态**：菜单默认处于折叠模式，只显示图标
- ✅ **Tooltip 提示**：鼠标悬停在折叠状态的菜单项上时显示名称
- ✅ **多级子菜单**：支持无限层级的子菜单展开/收起
- ✅ **激活状态**：当前激活的菜单项有特殊样式标识
- ✅ **SVG 图标**：使用 Lucide React 图标，支持自定义图标
- ✅ **徽章显示**：支持在菜单项上显示数字或文本徽章
- ✅ **响应式设计**：移动端自动切换为抽屉式菜单
- ✅ **键盘快捷键**：支持 Ctrl/Cmd + B 切换侧边栏
- ✅ **无障碍支持**：完整的键盘导航和屏幕阅读器支持

## 基本使用

```tsx
import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  CollapsibleSidebarMenu,
  MenuItem,
} from "@/components/ui/sidebar"
import { HomeIcon, SettingsIcon } from "lucide-react"

const menuItems: MenuItem[] = [
  {
    id: "home",
    label: "首页",
    icon: HomeIcon,
    isActive: true,
  },
  {
    id: "settings",
    label: "设置",
    icon: SettingsIcon,
  },
]

function App() {
  return (
    <SidebarProvider defaultOpen={false}>
      <div className="flex h-screen">
        <Sidebar collapsible="icon">
          <SidebarContent>
            <CollapsibleSidebarMenu items={menuItems} />
          </SidebarContent>
        </Sidebar>
        <main className="flex-1 p-6">
          {/* 主要内容 */}
        </main>
      </div>
    </SidebarProvider>
  )
}
```

## 菜单项配置

### MenuItem 接口

```tsx
interface MenuItem {
  id: string                    // 唯一标识符
  label: string                 // 显示名称
  icon?: React.ComponentType    // 图标组件
  href?: string                 // 链接地址
  onClick?: () => void          // 点击回调
  children?: MenuItem[]         // 子菜单项
  badge?: string | number       // 徽章内容
  isActive?: boolean           // 是否激活
  disabled?: boolean           // 是否禁用
}
```

### 多级菜单示例

```tsx
const menuItems: MenuItem[] = [
  {
    id: "projects",
    label: "项目管理",
    icon: FolderIcon,
    children: [
      {
        id: "all-projects",
        label: "所有项目",
        icon: FileIcon,
        href: "/projects",
      },
      {
        id: "my-projects",
        label: "我的项目",
        icon: UserIcon,
        href: "/projects/my",
        badge: 5,
      },
    ],
  },
]
```

## 组件 API

### SidebarProvider

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| defaultOpen | boolean | false | 默认是否展开 |
| open | boolean | - | 受控的展开状态 |
| onOpenChange | (open: boolean) => void | - | 展开状态变化回调 |

### CollapsibleSidebarMenu

| 属性 | 类型 | 描述 |
|------|------|------|
| items | MenuItem[] | 菜单项数组 |
| onItemClick | (item: MenuItem) => void | 菜单项点击回调 |
| className | string | 自定义样式类名 |

## 样式定制

组件使用 CSS 变量进行样式定制，可以通过修改以下变量来调整外观：

```css
:root {
  --sidebar-width: 16rem;
  --sidebar-width-icon: 4rem;
  --sidebar-background: hsl(var(--background));
  --sidebar-foreground: hsl(var(--foreground));
  --sidebar-accent: hsl(var(--accent));
  --sidebar-accent-foreground: hsl(var(--accent-foreground));
  --sidebar-border: hsl(var(--border));
}
```

## 完整示例

查看 `frontend/components/examples/collapsible-sidebar-example.tsx` 获取完整的使用示例。

## 迁移指南

如果你正在从旧的侧边栏组件迁移，主要变化包括：

1. 使用 `CollapsibleSidebarMenu` 替代手动构建菜单
2. 菜单配置改为数据驱动的 `MenuItem[]` 格式
3. 默认状态改为折叠模式
4. 新增了多级菜单和徽章支持

## 浏览器支持

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+
